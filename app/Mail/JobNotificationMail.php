<?php

namespace App\Mail;

use App\Models\JobNotificationCampaign;
use App\Models\JobNotificationRecipient;
use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Contracts\Queue\ShouldQueue;

class JobNotificationMail extends Mailable
{
    use Queueable, SerializesModels;

    /**
     * The job notification campaign instance.
     *
     * @var \App\Models\JobNotificationCampaign
     */
    public $campaign;

    /**
     * The job notification recipient instance.
     *
     * @var \App\Models\JobNotificationRecipient
     */
    public $recipient;

    /**
     * Create a new message instance.
     *
     * @param  \App\Models\JobNotificationCampaign  $campaign
     * @param  \App\Models\JobNotificationRecipient  $recipient
     * @return void
     */
    public function __construct(JobNotificationCampaign $campaign, JobNotificationRecipient $recipient)
    {
        $this->campaign = $campaign;
        $this->recipient = $recipient;
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        $businessName = $this->recipient->business_name 
            ?? $this->recipient->business?->name 
            ?? 'Business Owner';

        $business = $this->recipient->business;
        $jobId = $this->campaign->job_id;
        $token = $this->campaign->admin_token;
        $linkedUser = null;
        $link = null;
        $sendTo = $this->recipient->business_email;

        if ($business && $business->business_uuid) {
            $linkedUser = \App\Models\User::where('business_uuid', $business->business_uuid)->first();
            if ($linkedUser) {
                $link = url('/instant-job-details/' . $jobId . '?token=' . $token);
                $sendTo = $linkedUser->email;
            } else {
                $link = url('/instant-provider-dashboard?token=' . $token);
            }
        } else {
            $link = url('/instant-provider-dashboard?token=' . $token);
        }

        return $this->to($sendTo)
            ->subject('Looking for cleaner in ' . ($this->campaign->job_zip_code ?? $this->campaign->job_address ?? 'your area') . ' ASAP')
            ->markdown('emails.job-notification')
            ->with([
                'campaign' => $this->campaign,
                'recipient' => $this->recipient,
                'businessName' => $businessName,
                'jobTitle' => $this->campaign->job_title,
                'jobDescription' => $this->campaign->job_description,
                'jobBudget' => $this->campaign->job_budget,
                'jobZipCode' => $this->campaign->job_zip_code,
                'distance' => $this->recipient->distance,
                'link' => $link,
                'linkedUser' => $linkedUser,
                'jobId' => $jobId,
                'token' => $token,
            ]);
    }
} 