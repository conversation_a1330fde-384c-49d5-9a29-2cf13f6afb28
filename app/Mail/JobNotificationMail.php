<?php

namespace App\Mail;

use App\Models\JobNotificationCampaign;
use App\Models\JobNotificationRecipient;
use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Contracts\Queue\ShouldQueue;

class JobNotificationMail extends Mailable
{
    use Queueable, SerializesModels;

    /**
     * The job notification campaign instance.
     *
     * @var \App\Models\JobNotificationCampaign
     */
    public $campaign;

    /**
     * The job notification recipient instance.
     *
     * @var \App\Models\JobNotificationRecipient
     */
    public $recipient;

    /**
     * Create a new message instance.
     *
     * @param  \App\Models\JobNotificationCampaign  $campaign
     * @param  \App\Models\JobNotificationRecipient  $recipient
     * @return void
     */
    public function __construct(JobNotificationCampaign $campaign, JobNotificationRecipient $recipient)
    {
        $this->campaign = $campaign;
        $this->recipient = $recipient;
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        $businessName = $this->recipient->business_name 
            ?? $this->recipient->business?->name 
            ?? 'Business Owner';

        $business = $this->recipient->business;
        $jobId = $this->campaign->job_id;
        $token = $this->campaign->admin_token;
        $linkedUser = null;
        $link = null;
        $sendTo = $this->recipient->business_email;

        // Get the web app domain from environment variable
        $webAppDomain = rtrim(env('JOBON_WEBAPP_URL', 'https://jobon.app'), '/');

        // Check if the business email has a linked user
        $linkedUser = \App\Models\User::where('email', $this->recipient->business_email)->first();
        if ($linkedUser) {
            $link = $webAppDomain . '/instant-job-details/' . $jobId . '?token=' . $token;
            $sendTo = $linkedUser->email;
        } else {
            $link = $webAppDomain . '/instant-provider-dashboard?token=' . $token;
            $sendTo = $this->recipient->business_email;
        }

        return $this->to($sendTo)
            ->subject('Looking for cleaner in ' . ($this->campaign->job_zip_code ?? $this->campaign->job_address ?? 'your area') . ' ASAP')
            ->markdown('emails.job-notification')
            ->with([
                'campaign' => $this->campaign,
                'recipient' => $this->recipient,
                'businessName' => $businessName,
                'jobTitle' => $this->campaign->job_title,
                'jobDescription' => $this->campaign->job_description,
                'jobBudget' => $this->campaign->job_budget,
                'jobZipCode' => $this->campaign->job_zip_code,
                'distance' => $this->recipient->distance,
                'link' => $link,
                'linkedUser' => $linkedUser,
                'jobId' => $jobId,
                'token' => $token,
            ]);
    }
} 