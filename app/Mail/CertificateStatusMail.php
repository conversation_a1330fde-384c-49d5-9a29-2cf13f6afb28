<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;
use App\Models\User;

class CertificateStatusMail extends Mailable
{
    use Queueable, SerializesModels;

    public $user;
    public $action; // request_review, approved, rejected
    public $admin;

    public function __construct(User $user, string $action, $admin = null)
    {
        $this->user = $user;
        $this->action = $action;
        $this->admin = $admin;
    }

    public function build()
    {
        switch ($this->action) {
            case 'request_review':
                $subject = '[Certificates] Provider "' . $this->user->name . '" requested review';
                break;
            case 'approved':
                $subject = '[Certificates] Your certificates have been approved';
                break;
            case 'rejected':
                $subject = '[Certificates] Your certificates have been rejected';
                break;
            default:
                $subject = '[Certificates] Notification';
        }

        // Get the web app domain from environment variable
        $webAppDomain = rtrim(env('JOBON_WEBAPP_URL', 'https://jobon.app'), '/');
        $certificatesUrl = $webAppDomain . '/admin/certificates';

        return $this->subject($subject)
                    ->markdown('emails.certificate-status')
                    ->with([
                        'user' => $this->user,
                        'action' => $this->action,
                        'certificatesUrl' => $certificatesUrl,
                    ]);
    }
} 