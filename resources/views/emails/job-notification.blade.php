@component('mail::message')
# Looking for cleaner in {{ $campaign->job_address ?? $campaign->job_zip_code ?? 'your area' }} ASAP

Hi {{ $businessName }},

We have a customer in {{ $campaign->job_address ?? $campaign->job_zip_code ?? 'your area' }} who needs cleaning ASAP.
You’re getting this because we think you’re the right pro for the job.

**Job Details:**

{{ $jobTitle }}

@if($jobBudget)
Budget: ${{ number_format($jobBudget, 2) }}
@endif

Needs to be done in the next 3 days

Simple: dust, vacuum, kitchen, bath, trash out

No contracts, no monthly fees—just real jobs when you want them.

Interested?
@component('mail::button', ['url' => $link])
Claim the job
@endcomponent

Reply to this email if you have questions.
No B.S.—just jobs.

JobON.app | Real jobs. No B.S. Just results.
@endcomponent 