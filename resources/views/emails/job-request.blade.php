@component('mail::message')
# Looking for cleaner in {{$job->city}}, {{$job->state}} ASAP

Hi {{ $business->name }},

We have a customer in {{$job->city}}, {{$job->state}} who needs cleaning ASAP.
You're getting this because we think you're the right pro for the job.

**Job Details:**

Apartment cleaning

Budget: {{ $job->price ?? '[Price]' }}

Needs to be done in the next 3 days

Simple: dust, vacuum, kitchen, bath, trash out

No contracts, no monthly fees—just real jobs when you want them.

Interested?
@component('mail::button', ['url' => $jobUrl])
Click here to sign up and claim the job
@endcomponent

Reply to this email if you have questions.
No B.S.—just jobs.

JobON.app | Real jobs. No B.S. Just results.
@endcomponent 